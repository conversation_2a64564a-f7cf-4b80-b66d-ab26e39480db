import React from "react";
import StatisticsTable from "./components/StatisticsTable";
import { ApiProvider } from "./context/ApiContext";
import { ErrorBoundary } from "./components/ErrorBoundary";
import { DarkReaderProvider } from "./components/ui/DarkReaderProvider";

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <DarkReaderProvider
        enabled={true}
        config={{
          brightness: 150,
          contrast: 130,
          sepia: 0,
          grayscale: 0,
        }}
        autoDetectSystemTheme={false}
      >
        <ApiProvider>
          <div>
            <StatisticsTable />
          </div>
        </ApiProvider>
      </DarkReaderProvider>
    </ErrorBoundary>
  );
};

export default App;
