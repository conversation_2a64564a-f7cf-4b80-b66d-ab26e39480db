import { ColorType } from "lightweight-charts";

export const CHART_THEME = {
  layout: {
    background: { type: ColorType.Solid, color: "#181825" },
    textColor: "#cdd6f4",
  },
  grid: {
    vertLines: { color: "#313244" },
    horzLines: { color: "#313244" },
  },
  rightPriceScale: {
    borderColor: "#45475a",
  },
  crosshair: {
    vertLine: {
      color: "#94e2d5",
      labelBackgroundColor: "#45475a",
    },
    horzLine: {
      color: "#94e2d5",
      labelBackgroundColor: "#45475a",
    },
  },
} as const;

export const CHART_CONFIG = {
  dimensions: {
    width: 1200,
    height: 600,
  },
  timeScale: {
    timeVisible: true,
    secondsVisible: false,
  },
  crosshair: {
    mode: 1,
  },
  zoom: {
    speed: 1 / 30, // Faster zoom speed (1/3 instead of default 1/8)
  },
} as const;

export const SERIES_CONFIG = {
  candlestick: {
    upColor: "#00ff88",
    downColor: "#ff4444",
    borderVisible: false,
    wickUpColor: "#00ff88",
    wickDownColor: "#ff4444",
  },
  smma: {
    15: { color: "#edff00", lineWidth: 2 },
    29: { color: "#0033ff", lineWidth: 2 },
  },
} as const;

// Helper functions to get chart configurations
export const getChartConfig = () => ({
  ...CHART_CONFIG,
  ...CHART_THEME,
});

export const getSeriesConfig = () => SERIES_CONFIG;
