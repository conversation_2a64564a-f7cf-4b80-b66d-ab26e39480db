import type { CryptoCurrencyStatisticsDto, IndicatorValueDto } from "../generated";
import { CURRENCIES, DATA_PROCESSING } from "../constants/app";

export const filterByCurrency = (
  statistics: CryptoCurrencyStatisticsDto[],
  currency: string,
): CryptoCurrencyStatisticsDto[] => {
  return statistics.filter((crypto) => crypto.conversionCurrency === currency);
};

export const findBtcDataForSymbol = (
  btcStatistics: CryptoCurrencyStatisticsDto[],
  symbol: string,
): any => {
  return btcStatistics
    .find((el) => el.symbol === symbol)
    ?.indicatorValues?.find((el) => el);
};

export const formatDate = (isoString?: string): string => {
  if (!isoString) return DATA_PROCESSING.DEFAULT_VALUE;
  return new Date(isoString).toLocaleDateString();
};

export const processCryptoStatistics = (
  statistics: CryptoCurrencyStatisticsDto[],
) => {
  const usdStatistics = filterByCurrency(statistics, CURRENCIES.USD).sort(
    (a, b) => a.symbol.localeCompare(b.symbol),
  );
  const btcStatistics = filterByCurrency(statistics, CURRENCIES.BTC);

  return {
    usdStatistics,
    btcStatistics,
    totalCount: usdStatistics.length,
  };
};
