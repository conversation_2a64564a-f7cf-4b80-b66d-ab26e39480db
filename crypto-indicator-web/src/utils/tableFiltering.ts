import type { CryptoCurrencyStatisticsDto, IndicatorValueDto } from '../generated';
import type { FilterConfig } from '../types/table';

export const matchesSymbolSearch = (symbol: string, search: string): boolean => {
  if (!search.trim()) return true;
  return symbol.toLowerCase().includes(search.toLowerCase().trim());
};

export const matchesSignalFilter = (color: string | null | undefined, filter: string): boolean => {
  if (filter === 'all') return true;
  return color === filter;
};



export const applyFilters = (
  data: CryptoCurrencyStatisticsDto[],
  btcStatistics: CryptoCurrencyStatisticsDto[],
  filterConfig: FilterConfig,
  findBtcDataForSymbol: (btcStats: CryptoCurrencyStatisticsDto[], symbol: string) => IndicatorValueDto | undefined
): CryptoCurrencyStatisticsDto[] => {
  return data.filter((crypto) => {
    const usdData = crypto.indicatorValues?.[0];
    const btcData = findBtcDataForSymbol(btcStatistics, crypto.symbol);

    // Symbol search filter
    if (!matchesSymbolSearch(crypto.symbol, filterConfig.symbolSearch)) {
      return false;
    }

    // USD signal filter
    if (!matchesSignalFilter(usdData?.color, filterConfig.usdSignal)) {
      return false;
    }

    // BTC signal filter
    if (!matchesSignalFilter(btcData?.color, filterConfig.btcSignal)) {
      return false;
    }

    return true;
  });
};
