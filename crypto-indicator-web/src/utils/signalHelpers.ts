// Signal badge utility functions
export interface SignalInfo {
  className: string;
  icon: string;
  label: string;
  description: string;
  ariaLabel: string;
}

export const getSignalInfo = (signalColor?: string): SignalInfo => {
  switch (signalColor?.toLowerCase()) {
    case 'gold':
      return {
        className: 'signal-badge signal-gold',
        icon: '▲',
        label: 'Bullish',
        description: 'Positive trading signal - upward trend expected',
        ariaLabel: 'Bullish signal - click to view chart'
      };
    case 'blue':
      return {
        className: 'signal-badge signal-blue',
        icon: '▼',
        label: 'Bearish',
        description: 'Negative trading signal - downward trend expected',
        ariaLabel: 'Bearish signal - click to view chart'
      };
    case 'gray':
      return {
        className: 'signal-badge signal-gray',
        icon: '●',
        label: 'Neutral',
        description: 'Neutral signal - sideways movement or unclear trend',
        ariaLabel: 'Neutral signal - click to view chart'
      };
    default:
      return {
        className: 'signal-badge signal-default',
        icon: '?',
        label: 'Unknown',
        description: 'Signal data unavailable',
        ariaLabel: 'Unknown signal'
      };
  }
};

export const buildSignalClassName = (
  signalInfo: SignalInfo,
  isInteractive: boolean,
  loading: boolean,
  disabled: boolean
): string => {
  return [
    signalInfo.className,
    isInteractive ? 'clickable-signal' : '',
    loading ? 'signal-loading' : '',
    disabled ? 'signal-disabled' : ''
  ].filter(Boolean).join(' ');
};

export const getDisplayTitle = (
  title: string | undefined,
  isInteractive: boolean,
  signalInfo: SignalInfo
): string => {
  return title ?? (isInteractive ? `${signalInfo.description} • Click to view chart` : signalInfo.description);
};
