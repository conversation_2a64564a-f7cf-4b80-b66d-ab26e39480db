import { useState, useEffect } from 'react';

export type LayoutType = 'mobile' | 'tablet' | 'desktop';

interface ResponsiveConfig {
  mobile: number;
  tablet: number;
  desktop: number;
}

const DEFAULT_BREAKPOINTS: ResponsiveConfig = {
  mobile: 768,
  tablet: 1024,
  desktop: 1200,
};

export const useResponsiveLayout = (breakpoints: ResponsiveConfig = DEFAULT_BREAKPOINTS) => {
  const [layoutType, setLayoutType] = useState<LayoutType>('desktop');
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1200,
    height: typeof window !== 'undefined' ? window.innerHeight : 800,
  });

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setWindowSize({ width, height });
      
      if (width < breakpoints.mobile) {
        setLayoutType('mobile');
      } else if (width < breakpoints.tablet) {
        setLayoutType('tablet');
      } else {
        setLayoutType('desktop');
      }
    };

    // Set initial layout
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);
    
    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, [breakpoints]);

  const isMobile = layoutType === 'mobile';
  const isTablet = layoutType === 'tablet';
  const isDesktop = layoutType === 'desktop';
  const isMobileOrTablet = isMobile || isTablet;

  return {
    layoutType,
    windowSize,
    isMobile,
    isTablet,
    isDesktop,
    isMobileOrTablet,
    breakpoints,
  };
};
