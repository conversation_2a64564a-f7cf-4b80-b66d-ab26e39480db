import { useCallback } from "react";
import { RESPONSIVE_CONSTANTS } from "../constants/responsive";

interface UseTouchGesturesProps {
  onSwipeDown: () => void;
}

export const useTouchGestures = ({ onSwipeDown }: UseTouchGesturesProps) => {
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (e.touches.length === 0) return;
    const touch: Touch = e.touches[0] as Touch;
    const startY: number = touch.clientY;

    const handleTouchMove = (moveEvent: TouchEvent) => {
      if (moveEvent.touches.length === 0) return;
      const currentTouch: Touch = moveEvent.touches[0] as Touch;
      const deltaY: number = currentTouch.clientY - startY;

      // Close drawer if swiped down significantly
      if (deltaY > RESPONSIVE_CONSTANTS.SWIPE_THRESHOLD) {
        onSwipeDown();
        document.removeEventListener("touchmove", handleTouchMove);
      }
    };

    const handleTouchEnd = () => {
      document.removeEventListener("touchmove", handleTouchMove);
      document.removeEventListener("touchend", handleTouchEnd);
    };

    document.addEventListener("touchmove", handleTouchMove, { passive: false });
    document.addEventListener("touchend", handleTouchEnd);
  }, [onSwipeDown]);

  return { handleTouchStart };
};
