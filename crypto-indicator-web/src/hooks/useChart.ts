import { useEffect, useRef } from "react";
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "lightweight-charts";
import { chartHelpers } from "../utils/chartHelpers";
import type { CryptoCurrencyStatisticsDto } from "../generated";
import { CHART_CONSTANTS } from "../constants/chart";

interface UseChartReturn {
  chartRef: React.RefObject<IChartApi | null>;
  containerRef: React.RefObject<HTMLDivElement | null>;
}

/**
 * Hook for managing chart lifecycle and interactions
 * Handles chart creation, data setup, and cleanup
 */
export const useChart = (data: CryptoCurrencyStatisticsDto): UseChartReturn => {
  const chartRef = useRef<IChartApi | null>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    // Capture the current container reference for cleanup
    const currentContainer = containerRef.current;

    // Small delay to ensure container dimensions are calculated
    const timeoutId = setTimeout(() => {
      if (!containerRef.current) return;

      // Create chart instance
      const chart = chartHelpers.createChart(containerRef.current);
      chartRef.current = chart;

      // Setup series with data
      chartHelpers.setupChartSeries(chart, data);
    }, CHART_CONSTANTS.CHART_INIT_DELAY_MS);

    // Handle window resize
    const handleResize = () => {
      if (containerRef.current && chartRef.current) {
        chartHelpers.handleResize(chartRef.current, containerRef.current);
      }
    };

    // Handle fast zoom with mouse wheel
    const handleWheel = (event: WheelEvent) => {
      if (chartRef.current) {
        event.preventDefault();
        const zoomIn = event.deltaY < 0;
        chartHelpers.fastZoom(chartRef.current, zoomIn);
      }
    };

    window.addEventListener("resize", handleResize);

    if (currentContainer) {
      currentContainer.addEventListener("wheel", handleWheel, { passive: false });
    }

    // Cleanup function
    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener("resize", handleResize);
      if (currentContainer) {
        currentContainer.removeEventListener("wheel", handleWheel);
      }
      if (chartRef.current) {
        chartHelpers.cleanup(chartRef.current);
        chartRef.current = null;
      }
    };
  }, [data]);

  return {
    chartRef,
    containerRef,
  };
};
