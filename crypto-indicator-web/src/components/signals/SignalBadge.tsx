import React, { useState } from 'react';

interface SignalBadgeProps {
  color?: string | undefined;
  onClick?: () => void;
  clickable?: boolean;
  title?: string;
  loading?: boolean;
  disabled?: boolean;
}

export const SignalBadge: React.FC<SignalBadgeProps> = ({
  color,
  onClick,
  clickable = false,
  title,
  loading = false,
  disabled = false
}) => {
  const [showTooltip, setShowTooltip] = useState(false);

  const getSignalInfo = (signalColor?: string) => {
    switch (signalColor?.toLowerCase()) {
      case 'gold':
        return {
          className: 'signal-badge signal-gold',
          icon: '▲',
          label: 'Bullish',
          description: 'Positive trading signal - upward trend expected',
          ariaLabel: 'Bullish signal - click to view chart'
        };
      case 'blue':
        return {
          className: 'signal-badge signal-blue',
          icon: '▼',
          label: 'Bearish',
          description: 'Negative trading signal - downward trend expected',
          ariaLabel: 'Bearish signal - click to view chart'
        };
      case 'gray':
        return {
          className: 'signal-badge signal-gray',
          icon: '●',
          label: 'Neutral',
          description: 'Neutral signal - sideways movement or unclear trend',
          ariaLabel: 'Neutral signal - click to view chart'
        };
      default:
        return {
          className: 'signal-badge signal-default',
          icon: '?',
          label: 'Unknown',
          description: 'Signal data unavailable',
          ariaLabel: 'Unknown signal'
        };
    }
  };

  const signalInfo = getSignalInfo(color);
  const isInteractive = clickable && !disabled && !loading;

  const className = [
    signalInfo.className,
    isInteractive ? 'clickable-signal' : '',
    loading ? 'signal-loading' : '',
    disabled ? 'signal-disabled' : ''
  ].filter(Boolean).join(' ');

  const displayTitle = title ?? (isInteractive ? `${signalInfo.description} • Click to view chart` : signalInfo.description);

  const handleClick = () => {
    if (isInteractive && onClick) {
      onClick();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (isInteractive && (e.key === 'Enter' || e.key === ' ')) {
      e.preventDefault();
      onClick?.();
    }
  };

  return (
    <span
      className={className}
      onClick={handleClick}
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
      role={isInteractive ? 'button' : 'status'}
      tabIndex={isInteractive ? 0 : undefined}
      onKeyDown={handleKeyDown}
      aria-label={isInteractive ? signalInfo.ariaLabel : signalInfo.description}
      aria-disabled={disabled}
      aria-busy={loading}
    >
      {loading ? (
        <span className="signal-spinner" aria-hidden="true">⟳</span>
      ) : (
        <span className="signal-icon" aria-hidden="true">{signalInfo.icon}</span>
      )}
      <span className="signal-label">{signalInfo.label}</span>

      {displayTitle && (
        <div className={`signal-badge-tooltip ${showTooltip ? 'visible' : ''}`} role="tooltip">
          {displayTitle}
        </div>
      )}
    </span>
  );
};
