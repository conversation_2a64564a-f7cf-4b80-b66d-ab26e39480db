import React, { useEffect, useState, useMemo } from "react";
import { ChartModal } from "./chart/ChartModal";
import { LoadingState } from "./ui/LoadingState";
import { ErrorState } from "./ui/ErrorState";
import { ErrorBoundary } from "./ErrorBoundary";
import { DashboardHeader } from "./ui/DashboardHeader";
import { TableControls } from "./ui/TableControls";
import { ResponsiveTableContainer } from "./table/ResponsiveTableContainer";
import { useCryptoData } from "../hooks/useCryptoData";
import { useChartData } from "../hooks/useChartData";
import { useSorting } from "../hooks/useSorting";
import { useFiltering } from "../hooks/useFiltering";
import { processCryptoStatistics, findBtcDataForSymbol, formatDate } from "../utils/dataProcessors";
import { applySorting } from "../utils/tableSorting";
import { applyFilters } from "../utils/tableFiltering";
import { TIMING, CSS_CLASSES, UI_TEXT } from "../constants/app";

const StatisticsTable: React.FC = () => {
    const [showChart, setShowChart] = useState(false);

    const { data: statistics, loading, error, fetchData } = useCryptoData();
    const { chartData, chartLoading, chartError, fetchChartData, clearChartData } = useChartData();
    const { sortConfig, handleSort, getSortDirection } = useSorting();
    const {
        filterConfig,
        updateSymbolSearch,
        updateUsdSignal,
        updateBtcSignal,
        clearFilters,
        hasActiveFilters,
    } = useFiltering();

    // Process data with memoization for performance
    const { usdStatistics, btcStatistics, totalCount } = useMemo(() =>
        processCryptoStatistics(statistics), [statistics]
    );

    // Apply filtering and sorting with memoization for performance
    const processedData = useMemo(() => {
        const filtered = applyFilters(usdStatistics, btcStatistics, filterConfig, findBtcDataForSymbol);
        const sorted = applySorting(filtered, btcStatistics, sortConfig, findBtcDataForSymbol);
        return sorted;
    }, [usdStatistics, btcStatistics, filterConfig, sortConfig]);

    const filteredCount = processedData.length;

    const handleSignalClick = async (symbol: string, conversionCurrency: string) => {
        await fetchChartData(symbol, conversionCurrency);
        setShowChart(true);
    };

    const closeChart = () => {
        setShowChart(false);
        clearChartData();
    };

    useEffect(() => {
        void fetchData();
        const interval = setInterval(() => void fetchData(), TIMING.DATA_REFRESH_INTERVAL);
        return () => clearInterval(interval);
    }, [fetchData]);

    if (loading && statistics.length === 0) {
        return (
            <div className={CSS_CLASSES.APP_CONTAINER}>
                <DashboardHeader />
                <LoadingState message={UI_TEXT.LOADING_CRYPTO_DATA} />
            </div>
        );
    }

    if (error) {
        return (
            <div className={CSS_CLASSES.APP_CONTAINER}>
                <DashboardHeader />
                <ErrorState message={error} onRetry={fetchData} />
            </div>
        );
    }

    return (
        <div className={CSS_CLASSES.APP_CONTAINER}>
            <DashboardHeader />

            <div className={CSS_CLASSES.TABLE_CONTAINER}>
                <TableControls
                    totalCount={totalCount}
                    loading={loading}
                    onRefresh={fetchData}
                />

                <ResponsiveTableContainer
                    data={processedData}
                    btcStatistics={btcStatistics}
                    onSignalClick={handleSignalClick}
                    formatDate={formatDate}
                    findBtcDataForSymbol={findBtcDataForSymbol}
                    onSort={handleSort}
                    getSortDirection={getSortDirection}
                    filterConfig={filterConfig}
                    onSymbolSearchChange={updateSymbolSearch}
                    onUsdSignalChange={updateUsdSignal}
                    onBtcSignalChange={updateBtcSignal}
                    onClearFilters={clearFilters}
                    hasActiveFilters={hasActiveFilters}
                    filteredCount={filteredCount}
                    totalCount={totalCount}
                />
            </div>

            {showChart && chartData && (
                <ErrorBoundary>
                    <ChartModal data={chartData} onClose={closeChart} />
                </ErrorBoundary>
            )}

            {chartLoading && (
                <div className={CSS_CLASSES.CHART_MODAL}>
                    <div className={CSS_CLASSES.CHART_MODAL_CONTENT}>
                        <LoadingState message={UI_TEXT.LOADING_CHART_DATA} />
                    </div>
                </div>
            )}

            {chartError && (
                <div className={CSS_CLASSES.CHART_MODAL}>
                    <div className={CSS_CLASSES.CHART_MODAL_CONTENT}>
                        <ErrorState
                            message={chartError}
                            onRetry={closeChart}
                            showRetry={true}
                        />
                    </div>
                </div>
            )}
        </div>
    );
};

export default StatisticsTable;
