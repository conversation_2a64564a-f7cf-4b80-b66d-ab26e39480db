import React from "react";
import { ChartContainer } from "./ChartContainer";
import { ChartHeader } from "./ChartHeader";
import type { ChartModalProps } from "../../types/chart";

export const ChartModal: React.FC<ChartModalProps> = ({
  data,
  onClose,
}) => {
  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onClose();
      }
    };

    document.addEventListener("keydown", handleEscape);
    return () => document.removeEventListener("keydown", handleEscape);
  }, [onClose]);

  return (
    <div className="chart-modal" onClick={handleBackdropClick}>
      <div className="chart-modal-content">
        <ChartHeader
          symbol={data.symbol}
          conversionCurrency={data.conversionCurrency}
          onClose={onClose}
        />
        <ChartContainer data={data} />
      </div>
    </div>
  );
};
