import React, { createContext, useContext, ReactNode } from "react";
import { useDarkReader } from "../../hooks/useDarkReader";

interface DarkReaderConfig {
  brightness: number;
  contrast: number;
  sepia: number;
  grayscale?: number;
}

interface DarkReaderContextType {
  isEnabled: boolean;
  config: DarkReaderConfig;
  enable: (config?: DarkReaderConfig) => void;
  disable: () => void;
  toggle: () => void;
  updateConfig: (config: DarkReaderConfig) => void;
  exportCSS: () => Promise<string>;
  isDarkReaderSupported: boolean;
}

const DarkReaderContext = createContext<DarkReaderContextType | undefined>(
  undefined,
);

interface DarkReaderProviderProps {
  children: ReactNode;
  enabled?: boolean;
  config?: DarkReaderConfig;
  autoDetectSystemTheme?: boolean;
}

export const DarkReaderProvider: React.FC<DarkReaderProviderProps> = ({
  children,
  enabled = true,
  config = {
    brightness: 150,
    contrast: 130,
    sepia: 0,
    grayscale: 0,
  },
  autoDetectSystemTheme = false,
}) => {
  const darkReaderHook = useDarkReader({
    enabled,
    config,
    autoDetectSystemTheme,
  });

  return (
    <DarkReaderContext.Provider value={darkReaderHook}>
      {children}
    </DarkReaderContext.Provider>
  );
};

export const useDarkReaderContext = (): DarkReaderContextType => {
  const context = useContext(DarkReaderContext);
  if (context === undefined) {
    throw new Error(
      "useDarkReaderContext must be used within a DarkReaderProvider",
    );
  }
  return context;
};

export default DarkReaderProvider;
