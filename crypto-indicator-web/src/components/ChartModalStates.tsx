import React from "react";
import { ErrorBoundary } from "./ErrorBoundary";
import { ChartModal } from "./chart/ChartModal";
import { LoadingState } from "./ui/LoadingState";
import { ErrorState } from "./ui/ErrorState";
import { CSS_CLASSES, UI_TEXT } from "../constants/app";
import type { CryptoCurrencyStatisticsDto } from "../generated";

interface ChartModalStatesProps {
  showChart: boolean;
  chartData: CryptoCurrencyStatisticsDto | null;
  chartLoading: boolean;
  chartError: string | null;
  onClose: () => void;
}

export const ChartModalStates: React.FC<ChartModalStatesProps> = ({
  showChart,
  chartData,
  chartLoading,
  chartError,
  onClose,
}) => {
  return (
    <>
      {showChart && chartData && (
        <ErrorBoundary>
          <ChartModal data={chartData} onClose={onClose} />
        </ErrorBoundary>
      )}

      {chartLoading && (
        <div className={CSS_CLASSES.CHART_MODAL}>
          <div className={CSS_CLASSES.CHART_MODAL_CONTENT}>
            <LoadingState message={UI_TEXT.LOADING_CHART_DATA} />
          </div>
        </div>
      )}

      {chartError && (
        <div className={CSS_CLASSES.CHART_MODAL}>
          <div className={CSS_CLASSES.CHART_MODAL_CONTENT}>
            <ErrorState
              message={chartError}
              onRetry={onClose}
              showRetry={true}
            />
          </div>
        </div>
      )}
    </>
  );
};
