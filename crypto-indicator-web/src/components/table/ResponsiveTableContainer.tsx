import React, { useState } from 'react';
import type { CryptoCurrencyStatisticsDto, IndicatorValueDto } from '../../generated';
import type { SortColumn, SortDirection, FilterConfig } from '../../types/table';
import { useResponsiveLayout } from '../../hooks/useResponsiveLayout';
import { MobileLayout } from './MobileLayout';
import { TabletLayout } from './TabletLayout';
import { DesktopLayout } from './DesktopLayout';

interface ResponsiveTableContainerProps {
  data: CryptoCurrencyStatisticsDto[];
  btcStatistics: CryptoCurrencyStatisticsDto[];
  onSignalClick: (symbol: string, currency: string) => void;
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (btcStats: CryptoCurrencyStatisticsDto[], symbol: string) => IndicatorValueDto | undefined;
  
  // Sorting props
  onSort: (column: SortColumn) => void;
  getSortDirection: (column: SortColumn) => SortDirection;
  
  // Filtering props
  filterConfig: FilterConfig;
  onSymbolSearchChange: (search: string) => void;
  onUsdSignalChange: (signal: FilterConfig['usdSignal']) => void;
  onBtcSignalChange: (signal: FilterConfig['btcSignal']) => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
  filteredCount: number;
  totalCount: number;
}

export const ResponsiveTableContainer: React.FC<ResponsiveTableContainerProps> = ({
  data,
  btcStatistics,
  onSignalClick,
  formatDate,
  findBtcDataForSymbol,
  onSort,
  getSortDirection,
  filterConfig,
  onSymbolSearchChange,
  onUsdSignalChange,
  onBtcSignalChange,
  onClearFilters,
  hasActiveFilters,
  filteredCount,
  totalCount,
}) => {
  const { layoutType, isMobile, isTablet } = useResponsiveLayout();
  const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState(false);

  return (
    <div className={`responsive-table-container layout-${layoutType}`}>
      {isMobile && (
        <MobileLayout
          data={data}
          btcStatistics={btcStatistics}
          onSignalClick={onSignalClick}
          formatDate={formatDate}
          findBtcDataForSymbol={findBtcDataForSymbol}
          filterConfig={filterConfig}
          onUsdSignalChange={onUsdSignalChange}
          onBtcSignalChange={onBtcSignalChange}
          onClearFilters={onClearFilters}
          hasActiveFilters={hasActiveFilters}
          filteredCount={filteredCount}
          totalCount={totalCount}
          isFilterDrawerOpen={isFilterDrawerOpen}
          onFilterDrawerToggle={setIsFilterDrawerOpen}
        />
      )}
      {isTablet && (
        <TabletLayout
          data={data}
          btcStatistics={btcStatistics}
          onSignalClick={onSignalClick}
          formatDate={formatDate}
          findBtcDataForSymbol={findBtcDataForSymbol}
          filterConfig={filterConfig}
          onSymbolSearchChange={onSymbolSearchChange}
          onUsdSignalChange={onUsdSignalChange}
          onBtcSignalChange={onBtcSignalChange}
          onClearFilters={onClearFilters}
          hasActiveFilters={hasActiveFilters}
          filteredCount={filteredCount}
          totalCount={totalCount}
        />
      )}
      {!isMobile && !isTablet && (
        <DesktopLayout
          data={data}
          btcStatistics={btcStatistics}
          onSignalClick={onSignalClick}
          formatDate={formatDate}
          findBtcDataForSymbol={findBtcDataForSymbol}
          onSort={onSort}
          getSortDirection={getSortDirection}
          filterConfig={filterConfig}
          onSymbolSearchChange={onSymbolSearchChange}
          onUsdSignalChange={onUsdSignalChange}
          onBtcSignalChange={onBtcSignalChange}
          onClearFilters={onClearFilters}
          hasActiveFilters={hasActiveFilters}
          filteredCount={filteredCount}
          totalCount={totalCount}
        />
      )}
    </div>
  );
};
