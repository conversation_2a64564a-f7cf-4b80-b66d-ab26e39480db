import React, { useState } from 'react';
import type { CryptoCurrencyStatisticsDto, IndicatorValueDto } from '../../generated';
import type { SortColumn, SortDirection, FilterConfig } from '../../types/table';
import { useResponsiveLayout } from '../../hooks/useResponsiveLayout';
import { SortableTableHeader } from './SortableTableHeader';
import { CryptoTableRow } from './CryptoTableRow';
import { CryptoCard } from './CryptoCard';
import { TableFilters } from './TableFilters';
import { FilterDrawer } from './FilterDrawer';
import { FilterToggle } from './FilterToggle';
import { CSS_CLASSES } from '../../constants/app';

interface ResponsiveTableContainerProps {
  data: CryptoCurrencyStatisticsDto[];
  btcStatistics: CryptoCurrencyStatisticsDto[];
  onSignalClick: (symbol: string, currency: string) => void;
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (btcStats: CryptoCurrencyStatisticsDto[], symbol: string) => IndicatorValueDto | undefined;
  
  // Sorting props
  onSort: (column: SortColumn) => void;
  getSortDirection: (column: SortColumn) => SortDirection;
  
  // Filtering props
  filterConfig: FilterConfig;
  onSymbolSearchChange: (search: string) => void;
  onUsdSignalChange: (signal: FilterConfig['usdSignal']) => void;
  onBtcSignalChange: (signal: FilterConfig['btcSignal']) => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
  filteredCount: number;
  totalCount: number;
}

export const ResponsiveTableContainer: React.FC<ResponsiveTableContainerProps> = ({
  data,
  btcStatistics,
  onSignalClick,
  formatDate,
  findBtcDataForSymbol,
  onSort,
  getSortDirection,
  filterConfig,
  onSymbolSearchChange,
  onUsdSignalChange,
  onBtcSignalChange,
  onClearFilters,
  hasActiveFilters,
  filteredCount,
  totalCount,
}) => {
  const { layoutType, isMobile, isTablet } = useResponsiveLayout();
  const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState(false);

  const renderMobileLayout = () => (
    <div className="mobile-layout">
      <FilterToggle
        onClick={() => setIsFilterDrawerOpen(true)}
        hasActiveFilters={hasActiveFilters}
        filteredCount={filteredCount}
        totalCount={totalCount}
      />
      
      <div className="crypto-cards-container">
        {data.map((crypto) => {
          const btcData = findBtcDataForSymbol(btcStatistics, crypto.symbol);
          return (
            <CryptoCard
              key={crypto.symbol}
              crypto={crypto}
              btcData={btcData}
              onSignalClick={onSignalClick}
              formatDate={formatDate}
            />
          );
        })}
      </div>

      <FilterDrawer
        isOpen={isFilterDrawerOpen}
        onClose={() => setIsFilterDrawerOpen(false)}
        filterConfig={filterConfig}
        onUsdSignalChange={onUsdSignalChange}
        onBtcSignalChange={onBtcSignalChange}
        onClearFilters={onClearFilters}
        hasActiveFilters={hasActiveFilters}
        filteredCount={filteredCount}
        totalCount={totalCount}
      />
    </div>
  );

  const renderTabletLayout = () => (
    <div className="tablet-layout">
      <TableFilters
        filterConfig={filterConfig}
        onSymbolSearchChange={onSymbolSearchChange}
        onUsdSignalChange={onUsdSignalChange}
        onBtcSignalChange={onBtcSignalChange}
        onClearFilters={onClearFilters}
        hasActiveFilters={hasActiveFilters}
        filteredCount={filteredCount}
        totalCount={totalCount}
      />
      
      <div className="crypto-cards-grid">
        {data.map((crypto) => {
          const btcData = findBtcDataForSymbol(btcStatistics, crypto.symbol);
          return (
            <CryptoCard
              key={crypto.symbol}
              crypto={crypto}
              btcData={btcData}
              onSignalClick={onSignalClick}
              formatDate={formatDate}
            />
          );
        })}
      </div>
    </div>
  );

  const renderDesktopLayout = () => (
    <div className="desktop-layout">
      <TableFilters
        filterConfig={filterConfig}
        onSymbolSearchChange={onSymbolSearchChange}
        onUsdSignalChange={onUsdSignalChange}
        onBtcSignalChange={onBtcSignalChange}
        onClearFilters={onClearFilters}
        hasActiveFilters={hasActiveFilters}
        filteredCount={filteredCount}
        totalCount={totalCount}
      />

      <div className="table-wrapper">
        <table className={CSS_CLASSES.TABLE}>
          <SortableTableHeader
            onSort={onSort}
            getSortDirection={getSortDirection}
          />
          <tbody>
            {data.map((crypto) => {
              const btcData = findBtcDataForSymbol(btcStatistics, crypto.symbol);
              return (
                <CryptoTableRow
                  key={crypto.symbol}
                  crypto={crypto}
                  btcData={btcData}
                  onSignalClick={onSignalClick}
                  formatDate={formatDate}
                />
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );

  return (
    <div className={`responsive-table-container layout-${layoutType}`}>
      {isMobile && renderMobileLayout()}
      {isTablet && renderTabletLayout()}
      {!isMobile && !isTablet && renderDesktopLayout()}
    </div>
  );
};
