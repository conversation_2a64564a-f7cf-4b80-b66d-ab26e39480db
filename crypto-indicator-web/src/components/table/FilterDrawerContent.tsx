import React from "react";
import type { FilterConfig } from "../../types/table";

interface FilterDrawerContentProps {
  filterConfig: FilterConfig;
  onUsdSignalChange: (signal: FilterConfig["usdSignal"]) => void;
  onBtcSignalChange: (signal: FilterConfig["btcSignal"]) => void;
}

export const FilterDrawerContent: React.FC<FilterDrawerContentProps> = ({
  filterConfig,
  onUsdSignalChange,
  onBtcSignalChange,
}) => {
  return (
    <div className="filter-drawer-content">
      <div className="filter-section">
        <label htmlFor="mobile-usd-signal">USD Signal</label>
        <select
          id="mobile-usd-signal"
          value={filterConfig.usdSignal}
          onChange={(e) =>
            onUsdSignalChange(e.target.value as FilterConfig["usdSignal"])
          }
          className="filter-drawer-select"
        >
          <option value="all">All Signals</option>
          <option value="gold">🟡 Gold (Bullish)</option>
          <option value="blue">🔵 Blue (Bearish)</option>
          <option value="gray">⚪ Gray (Neutral)</option>
        </select>
      </div>

      <div className="filter-section">
        <label htmlFor="mobile-btc-signal">BTC Signal</label>
        <select
          id="mobile-btc-signal"
          value={filterConfig.btcSignal}
          onChange={(e) =>
            onBtcSignalChange(e.target.value as FilterConfig["btcSignal"])
          }
          className="filter-drawer-select"
        >
          <option value="all">All Signals</option>
          <option value="gold">🟡 Gold (Bullish)</option>
          <option value="blue">🔵 Blue (Bearish)</option>
          <option value="gray">⚪ Gray (Neutral)</option>
        </select>
      </div>
    </div>
  );
};
