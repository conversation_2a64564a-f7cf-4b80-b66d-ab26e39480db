import React from "react";
import { CryptoTableRow } from "./CryptoTableRow";
import { SortableTableHeader } from "./SortableTableHeader";
import { TableFilters } from "./TableFilters";
import { CSS_CLASSES } from "../../constants/app";
import type { CryptoCurrencyStatisticsDto, IndicatorValueDto } from "../../generated";
import type { SortColumn, SortDirection, FilterConfig } from "../../types/table";

interface DesktopLayoutProps {
  data: CryptoCurrencyStatisticsDto[];
  btcStatistics: CryptoCurrencyStatisticsDto[];
  onSignalClick: (symbol: string, currency: string) => void;
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (btcStats: CryptoCurrencyStatisticsDto[], symbol: string) => IndicatorValueDto | undefined;
  onSort: (column: SortColumn) => void;
  getSortDirection: (column: SortColumn) => SortDirection;
  filterConfig: FilterConfig;
  onSymbolSearchChange: (search: string) => void;
  onUsdSignalChange: (signal: FilterConfig["usdSignal"]) => void;
  onBtcSignalChange: (signal: FilterConfig["btcSignal"]) => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
  filteredCount: number;
  totalCount: number;
}

export const DesktopLayout: React.FC<DesktopLayoutProps> = ({
  data,
  btcStatistics,
  onSignalClick,
  formatDate,
  findBtcDataForSymbol,
  onSort,
  getSortDirection,
  filterConfig,
  onSymbolSearchChange,
  onUsdSignalChange,
  onBtcSignalChange,
  onClearFilters,
  hasActiveFilters,
  filteredCount,
  totalCount,
}) => {
  return (
    <div className="desktop-layout">
      <TableFilters
        filterConfig={filterConfig}
        onSymbolSearchChange={onSymbolSearchChange}
        onUsdSignalChange={onUsdSignalChange}
        onBtcSignalChange={onBtcSignalChange}
        onClearFilters={onClearFilters}
        hasActiveFilters={hasActiveFilters}
        filteredCount={filteredCount}
        totalCount={totalCount}
      />

      <div className="table-wrapper">
        <table className={CSS_CLASSES.TABLE}>
          <SortableTableHeader
            onSort={onSort}
            getSortDirection={getSortDirection}
          />
          <tbody>
            {data.map((crypto) => {
              const btcData = findBtcDataForSymbol(btcStatistics, crypto.symbol);
              return (
                <CryptoTableRow
                  key={crypto.symbol}
                  crypto={crypto}
                  btcData={btcData}
                  onSignalClick={onSignalClick}
                  formatDate={formatDate}
                />
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};
