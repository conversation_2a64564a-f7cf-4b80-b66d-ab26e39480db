import React from "react";
import { CryptoCard } from "./CryptoCard";
import { TableFilters } from "./TableFilters";
import type { CryptoCurrencyStatisticsDto, IndicatorValueDto } from "../../generated";
import type { FilterConfig } from "../../types/table";

interface TabletLayoutProps {
  data: CryptoCurrencyStatisticsDto[];
  btcStatistics: CryptoCurrencyStatisticsDto[];
  onSignalClick: (symbol: string, currency: string) => void;
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (btcStats: CryptoCurrencyStatisticsDto[], symbol: string) => IndicatorValueDto | undefined;
  filterConfig: FilterConfig;
  onSymbolSearchChange: (search: string) => void;
  onUsdSignalChange: (signal: FilterConfig["usdSignal"]) => void;
  onBtcSignalChange: (signal: FilterConfig["btcSignal"]) => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
  filteredCount: number;
  totalCount: number;
}

export const TabletLayout: React.FC<TabletLayoutProps> = ({
  data,
  btcStatistics,
  onSignalClick,
  formatDate,
  findBtcDataForSymbol,
  filterConfig,
  onSymbolSearchChange,
  onUsdSignalChange,
  onBtcSignalChange,
  onClearFilters,
  hasActiveFilters,
  filteredCount,
  totalCount,
}) => {
  return (
    <div className="tablet-layout">
      <TableFilters
        filterConfig={filterConfig}
        onSymbolSearchChange={onSymbolSearchChange}
        onUsdSignalChange={onUsdSignalChange}
        onBtcSignalChange={onBtcSignalChange}
        onClearFilters={onClearFilters}
        hasActiveFilters={hasActiveFilters}
        filteredCount={filteredCount}
        totalCount={totalCount}
      />
      
      <div className="crypto-cards-grid">
        {data.map((crypto) => {
          const btcData = findBtcDataForSymbol(btcStatistics, crypto.symbol);
          return (
            <CryptoCard
              key={crypto.symbol}
              crypto={crypto}
              btcData={btcData}
              onSignalClick={onSignalClick}
              formatDate={formatDate}
            />
          );
        })}
      </div>
    </div>
  );
};
