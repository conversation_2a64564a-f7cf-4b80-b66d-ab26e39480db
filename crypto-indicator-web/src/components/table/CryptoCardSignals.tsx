import React from "react";
import { SignalBadge } from "../signals/SignalBadge";
import { CURRENCIES } from "../../constants/app";
import type { IndicatorValueDto } from "../../generated";

interface CryptoCardSignalsProps {
  symbol: string;
  usdData: IndicatorValueDto | undefined;
  btcData: IndicatorValueDto | undefined;
  usdTooltip: string;
  btcTooltip: string;
  onSignalClick: (symbol: string, currency: string) => void;
}

export const CryptoCardSignals: React.FC<CryptoCardSignalsProps> = ({
  symbol,
  usdData,
  btcData,
  usdTooltip,
  btcTooltip,
  onSignalClick,
}) => {
  return (
    <div className="crypto-card-signals">
      <div className="signal-group">
        <span className="signal-label">USD Signal</span>
        <div onClick={(e) => e.stopPropagation()}>
          <SignalBadge
            color={usdData?.color}
            onClick={() => onSignalClick(symbol, CURRENCIES.USD)}
            clickable={true}
            title={usdTooltip}
          />
        </div>
      </div>
      <div className="signal-group">
        <span className="signal-label">BTC Signal</span>
        <div onClick={(e) => e.stopPropagation()}>
          <SignalBadge
            color={btcData?.color}
            onClick={() => onSignalClick(symbol, CURRENCIES.BTC)}
            clickable={true}
            title={btcTooltip}
          />
        </div>
      </div>
    </div>
  );
};
