import React from "react";
import { CryptoCard } from "./CryptoCard";
import { FilterToggle } from "./FilterToggle";
import { FilterDrawer } from "./FilterDrawer";
import type { CryptoCurrencyStatisticsDto, IndicatorValueDto } from "../../generated";
import type { FilterConfig } from "../../types/table";

interface MobileLayoutProps {
  data: CryptoCurrencyStatisticsDto[];
  btcStatistics: CryptoCurrencyStatisticsDto[];
  onSignalClick: (symbol: string, currency: string) => void;
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (btcStats: CryptoCurrencyStatisticsDto[], symbol: string) => IndicatorValueDto | undefined;
  filterConfig: FilterConfig;
  onUsdSignalChange: (signal: FilterConfig["usdSignal"]) => void;
  onBtcSignalChange: (signal: FilterConfig["btcSignal"]) => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
  filteredCount: number;
  totalCount: number;
  isFilterDrawerOpen: boolean;
  onFilterDrawerToggle: (open: boolean) => void;
}

export const MobileLayout: React.FC<MobileLayoutProps> = ({
  data,
  btcStatistics,
  onSignalClick,
  formatDate,
  findBtcDataForSymbol,
  filterConfig,
  onUsdSignalChange,
  onBtcSignalChange,
  onClearFilters,
  hasActiveFilters,
  filteredCount,
  totalCount,
  isFilterDrawerOpen,
  onFilterDrawerToggle,
}) => {
  return (
    <div className="mobile-layout">
      <FilterToggle
        onClick={() => onFilterDrawerToggle(true)}
        hasActiveFilters={hasActiveFilters}
        filteredCount={filteredCount}
        totalCount={totalCount}
      />
      
      <div className="crypto-cards-container">
        {data.map((crypto) => {
          const btcData = findBtcDataForSymbol(btcStatistics, crypto.symbol);
          return (
            <CryptoCard
              key={crypto.symbol}
              crypto={crypto}
              btcData={btcData}
              onSignalClick={onSignalClick}
              formatDate={formatDate}
            />
          );
        })}
      </div>

      <FilterDrawer
        isOpen={isFilterDrawerOpen}
        onClose={() => onFilterDrawerToggle(false)}
        filterConfig={filterConfig}
        onUsdSignalChange={onUsdSignalChange}
        onBtcSignalChange={onBtcSignalChange}
        onClearFilters={onClearFilters}
        hasActiveFilters={hasActiveFilters}
        filteredCount={filteredCount}
        totalCount={totalCount}
      />
    </div>
  );
};
