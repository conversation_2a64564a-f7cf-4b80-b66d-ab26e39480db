import React, { useState } from "react";
import type { CryptoCurrencyStatisticsDto, IndicatorValueDto } from "../../generated";
import { navigation } from "../../utils/formatters";
import { CryptoCardHeader } from "./CryptoCardHeader";
import { CryptoCardSignals } from "./CryptoCardSignals";
import { CryptoCardDetails } from "./CryptoCardDetails";

interface CryptoCardProps {
  crypto: CryptoCurrencyStatisticsDto;
  btcData?: IndicatorValueDto;
  onSignalClick: (symbol: string, currency: string) => void;
  formatDate: (date?: string) => string;
}

export const CryptoCard: React.FC<CryptoCardProps> = ({
  crypto,
  btcData,
  onSignalClick,
  formatDate,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const usdData = crypto.indicatorValues.find((el) => el);

  const usdTooltip = `Click to view chart${
    usdData?.timestamp ? ` • Last update: ${formatDate(usdData.timestamp)}` : ""
  }`;
  const btcTooltip = `Click to view chart${
    btcData?.timestamp ? ` • Last update: ${formatDate(btcData.timestamp)}` : ""
  }`;

  const handleSymbolClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card expansion
    navigation.openCoinMarketCap(crypto.mapping?.slug);
  };

  return (
    <div className="crypto-card" onClick={() => setIsExpanded(!isExpanded)}>
      <CryptoCardHeader
        crypto={crypto}
        usdData={usdData}
        onSymbolClick={handleSymbolClick}
      />

      <CryptoCardSignals
        symbol={crypto.symbol}
        usdData={usdData}
        btcData={btcData}
        usdTooltip={usdTooltip}
        btcTooltip={btcTooltip}
        onSignalClick={onSignalClick}
      />

      {isExpanded && (
        <CryptoCardDetails
          usdData={usdData}
          btcData={btcData}
          formatDate={formatDate}
        />
      )}

      <div className="crypto-card-expand-indicator">
        <span className={`expand-icon ${isExpanded ? "expanded" : ""}`}>▼</span>
      </div>
    </div>
  );
};
