import React from "react";
import { formatters } from "../../utils/formatters";
import { CURRENCIES, CSS_CLASSES } from "../../constants/app";
import type { CryptoCurrencyStatisticsDto, IndicatorValueDto } from "../../generated";

interface CryptoCardHeaderProps {
  crypto: CryptoCurrencyStatisticsDto;
  usdData: IndicatorValueDto | undefined;
  onSymbolClick: (e: React.MouseEvent) => void;
}

export const CryptoCardHeader: React.FC<CryptoCardHeaderProps> = ({
  crypto,
  usdData,
  onSymbolClick,
}) => {
  return (
    <div className="crypto-card-header">
      <div className="crypto-card-symbol">
        <div className={CSS_CLASSES.SYMBOL_CELL}>
          <strong
            className="clickable-symbol"
            onClick={onSymbolClick}
            title={`🔗 Click to view ${crypto.symbol} on CoinMarketCap (opens in new tab)`}
          >
            {crypto.symbol}
          </strong>
        </div>
      </div>
      <div className="crypto-card-price">
        <span className="price-label">USD</span>
        <span className="price-value">
          {formatters.price(usdData?.close, CURRENCIES.USD)}
        </span>
      </div>
    </div>
  );
};
