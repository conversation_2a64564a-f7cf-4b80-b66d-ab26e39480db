import React from "react";
import { formatters } from "../../utils/formatters";
import { CURRENCIES } from "../../constants/app";
import type { IndicatorValueDto } from "../../generated";

interface CryptoCardDetailsProps {
  usdData: IndicatorValueDto | undefined;
  btcData: IndicatorValueDto | undefined;
  formatDate: (date?: string) => string;
}

export const CryptoCardDetails: React.FC<CryptoCardDetailsProps> = ({
  usdData,
  btcData,
  formatDate,
}) => {
  return (
    <div className="crypto-card-details">
      <div className="detail-row">
        <span className="detail-label">Market Cap</span>
        <span className="detail-value">
          {formatters.marketCap(usdData?.marketCap)}
        </span>
      </div>
      <div className="detail-row">
        <span className="detail-label">BTC Price</span>
        <span className="detail-value">
          {formatters.price(btcData?.close, CURRENCIES.BTC)}
        </span>
      </div>
      {usdData?.timestamp && (
        <div className="detail-row">
          <span className="detail-label">Last Update</span>
          <span className="detail-value">
            {formatDate(usdData.timestamp)}
          </span>
        </div>
      )}
    </div>
  );
};
