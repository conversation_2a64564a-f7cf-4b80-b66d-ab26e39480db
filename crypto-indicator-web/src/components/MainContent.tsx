import React from "react";
import { ResponsiveTableContainer } from "./table/ResponsiveTableContainer";
import { TableControls } from "./ui/TableControls";
import { CSS_CLASSES } from "../constants/app";
import type { CryptoCurrencyStatisticsDto } from "../generated";
import type { FilterConfig } from "../types/table";

interface MainContentProps {
  processedData: CryptoCurrencyStatisticsDto[];
  btcStatistics: CryptoCurrencyStatisticsDto[];
  totalCount: number;
  filteredCount: number;
  loading: boolean;
  filterConfig: FilterConfig;
  hasActiveFilters: boolean;
  onSignalClick: (symbol: string, currency: string) => Promise<void>;
  onRefresh: () => void;
  onSort: (field: string) => void;
  getSortDirection: (field: string) => "asc" | "desc" | null;
  onSymbolSearchChange: (search: string) => void;
  onUsdSignalChange: (signal: FilterConfig["usdSignal"]) => void;
  onBtcSignalChange: (signal: FilterConfig["btcSignal"]) => void;
  onClearFilters: () => void;
  formatDate: (date: string) => string;
  findBtcDataForSymbol: (symbol: string) => CryptoCurrencyStatisticsDto | undefined;
}

export const MainContent: React.FC<MainContentProps> = ({
  processedData,
  btcStatistics,
  totalCount,
  filteredCount,
  loading,
  filterConfig,
  hasActiveFilters,
  onSignalClick,
  onRefresh,
  onSort,
  getSortDirection,
  onSymbolSearchChange,
  onUsdSignalChange,
  onBtcSignalChange,
  onClearFilters,
  formatDate,
  findBtcDataForSymbol,
}) => {
  return (
    <div className={CSS_CLASSES.TABLE_CONTAINER}>
      <TableControls
        totalCount={totalCount}
        loading={loading}
        onRefresh={onRefresh}
      />

      <ResponsiveTableContainer
        data={processedData}
        btcStatistics={btcStatistics}
        onSignalClick={(symbol, currency) => { onSignalClick(symbol, currency).catch(console.error); }}
        formatDate={formatDate}
        findBtcDataForSymbol={findBtcDataForSymbol}
        onSort={onSort}
        getSortDirection={getSortDirection}
        filterConfig={filterConfig}
        onSymbolSearchChange={onSymbolSearchChange}
        onUsdSignalChange={onUsdSignalChange}
        onBtcSignalChange={onBtcSignalChange}
        onClearFilters={onClearFilters}
        hasActiveFilters={hasActiveFilters}
        filteredCount={filteredCount}
        totalCount={totalCount}
      />
    </div>
  );
};
