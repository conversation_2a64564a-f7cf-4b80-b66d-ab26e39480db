import React from "react";
import { LoadingState } from "./ui/LoadingState";
import { ErrorState } from "./ui/ErrorState";
import { DashboardHeader } from "./ui/DashboardHeader";
import { CSS_CLASSES, UI_TEXT } from "../constants/app";

interface AppStateHandlerProps {
  loading: boolean;
  error: string | null;
  statisticsLength: number;
  onRetry: () => void;
  children: React.ReactNode;
}

export const AppStateHandler: React.FC<AppStateHandlerProps> = ({
  loading,
  error,
  statisticsLength,
  onRetry,
  children,
}) => {
  if (loading && statisticsLength === 0) {
    return (
      <div className={CSS_CLASSES.APP_CONTAINER}>
        <DashboardHeader />
        <LoadingState message={UI_TEXT.LOADING_CRYPTO_DATA} />
      </div>
    );
  }

  if (error) {
    return (
      <div className={CSS_CLASSES.APP_CONTAINER}>
        <DashboardHeader />
        <ErrorState message={error} onRetry={onRetry} />
      </div>
    );
  }

  return <>{children}</>;
};
